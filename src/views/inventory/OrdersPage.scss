@use '@/styles/variables' as *;

/**
 * 订单页面样式
 */

.start-bill-page {
  padding: $spacing-medium;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: $spacing-medium;
}

.filter-card {
  margin-bottom: 0;
}

.toolbar {
  margin-bottom: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: $spacing-medium;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: $font-weight-medium;
  line-height: 32px;
  color: $text-color-primary;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: $spacing-small;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: $spacing-small;
}

.custom-date-picker {
  width: 280px;
}

.amount-range {
  display: flex;
  align-items: center;
  gap: $spacing-small;
}

.amount-separator {
  color: $text-color-secondary;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: $spacing-small;
}

:deep(.department-selector) {
  width: 350px !important;
  min-width: 350px !important;
}

/* 自定义单选按钮组样式 */
:deep(.custom-radio-group .custom-radio-button) {
  border: none;
  background: transparent;
  transition: all $transition-duration $transition-timing-function;
}

:deep(.custom-radio-group .custom-radio-button:hover) {
  color: $primary-color;
  background-color: $primary-color-light;
}

:deep(.custom-radio-group .custom-radio-button--checked) {
  color: #fff !important;
  background-color: $primary-color !important;
  border: none !important;
}

.n-data-table {
  flex: 1;
  overflow: auto;
}

/* 表格滚动样式 */
:deep(.n-data-table-base-table-body) {
  overflow-x: auto !important;
}

/* 冻结列样式 */
:deep(.n-data-table-thead .n-data-table-th.n-data-table-th--fixed-left),
:deep(.n-data-table-tbody .n-data-table-td.n-data-table-td--fixed-left) {
  background-color: var(--n-merged-th-color);
  box-shadow: $box-shadow-light;
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: $spacing-base;
  padding-bottom: $spacing-base;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: $spacing-base;
  padding-bottom: $spacing-base;
}
